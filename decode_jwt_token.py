#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import base64
import json

# 从HTML页面中提取的JWT token
jwt_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhcHBfa2V5IjoiODFjNGU2NmIwNDA2NmFhOGRhMDc5ZjNlNWQyYmYxZjMiLCJlbnZpcm9ubWVudCI6NiwibWVyY2hhbnRfaWQiOjEwMTM2OCwidXNlcl9hY2NvdW50IjoiMTM5OTU5MzMwNTMiLCJ1c2VyX2lkIjoyNTg1LCJ1c2VyX2NvZGUiOm51bGwsInVzZXJfbmFtZSI6IuaxquazoiIsImV4cGlyYXRpb25fdGltZSI6MTc1NDAyODM5OSwiSXNBZG1pbiI6dHJ1ZSwiQ3VycmVudFJpc2tQZXJtaXNzaW9uVHlwZSI6WzEsMiwzLDQsNSw3LDgsOV0sImRhdGFfcGVybWlzc2lvbl90eXBlIjoxLCJkZXBhcnRtZW50X2lkIjo2NzN9.7VxgQhZNvWF0_y838jNX813pCiL6zSXw_VRA1ds_5rg"

# currentAccessToken
current_access_token = "142B612CB94A1EECC7A44AC8C68BB9D749D7AE21"

print("=== JWT Token 解码 ===")
print(f"JWT Token: {jwt_token}")
print(f"Current Access Token: {current_access_token}")

# JWT token 由三部分组成，用.分隔
parts = jwt_token.split('.')

if len(parts) == 3:
    header, payload, signature = parts
    
    print("\n=== Header 解码 ===")
    try:
        # 添加padding如果需要
        header_padded = header + '=' * (4 - len(header) % 4)
        header_decoded = base64.b64decode(header_padded).decode('utf-8')
        header_json = json.loads(header_decoded)
        print(json.dumps(header_json, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"Header解码失败: {e}")
    
    print("\n=== Payload 解码 ===")
    try:
        # 添加padding如果需要
        payload_padded = payload + '=' * (4 - len(payload) % 4)
        payload_decoded = base64.b64decode(payload_padded).decode('utf-8')
        payload_json = json.loads(payload_decoded)
        print(json.dumps(payload_json, indent=2, ensure_ascii=False))
        
        # 分析关键字段
        print("\n=== 关键字段分析 ===")
        if 'app_key' in payload_json:
            print(f"App Key: {payload_json['app_key']}")
        if 'user_account' in payload_json:
            print(f"User Account: {payload_json['user_account']}")
        if 'user_id' in payload_json:
            print(f"User ID: {payload_json['user_id']}")
        if 'merchant_id' in payload_json:
            print(f"Merchant ID: {payload_json['merchant_id']}")
        if 'expiration_time' in payload_json:
            import datetime
            exp_time = payload_json['expiration_time']
            exp_datetime = datetime.datetime.fromtimestamp(exp_time)
            print(f"过期时间: {exp_time} ({exp_datetime})")
            
    except Exception as e:
        print(f"Payload解码失败: {e}")
    
    print(f"\n=== Signature ===")
    print(f"Signature: {signature}")

print(f"\n=== Current Access Token 分析 ===")
print(f"长度: {len(current_access_token)}")
print(f"字符集: {sorted(set(current_access_token))}")

# 检查是否是我们之前捕获的access_code之一
known_access_codes = [
    "0ED644807DC4876D6771F992CC08FF97",
    "6D34DFCDAC40CAD1033CB633BFE78876", 
    "4ED6EF22C6B165704CF33415CBBB2D9",
    "D358CD4F780378EE63257EBB591B6129"
]

print(f"\n=== 与已知access_code比较 ===")
if current_access_token in known_access_codes:
    print("✓ 这个currentAccessToken就是我们之前捕获的access_code之一!")
else:
    print("✗ 这个currentAccessToken不在我们之前捕获的access_code列表中")
    print("已知的access_codes:")
    for i, code in enumerate(known_access_codes, 1):
        print(f"  {i}. {code}")

print(f"\n=== 重要发现：两套Token系统 ===")
print("从index.html分析发现：")
print("1. JWT Token: 用于跨域应用认证，包含用户信息，有效期到2025-08-01")
print("2. currentAccessToken: 40字符，可能是会话级别的访问令牌")
print("3. access_code: 32字符MD5，用于登录验证，有时效性")
print("\n在JavaScript代码中发现关键函数 admplink():")
print("  - 调用 /Account/UserInfo/GetAdmpCode 获取 accessCode")
print("  - 这可能就是我们需要的access_code生成API!")
print("\n建议下一步:")
print("1. 分析 /Account/UserInfo/GetAdmpCode API")
print("2. 理解access_code的生成逻辑")
print("3. 修改Python脚本使用正确的API获取access_code")
