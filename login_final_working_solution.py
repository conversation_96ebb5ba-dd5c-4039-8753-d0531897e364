import requests
import hashlib
import time
import base64
import json
from urllib.parse import quote, urljoin, unquote
import urllib.parse
import os

# 修补Pillow兼容性问题
def patch_pillow_compatibility():
    """修补Pillow兼容性问题"""
    try:
        from PIL import Image
        if not hasattr(Image, 'ANTIALIAS'):
            Image.ANTIALIAS = Image.LANCZOS
            print("[+] 已修补Pillow兼容性问题")
    except:
        pass

# 尝试导入ddddocr
DDDDOCR_AVAILABLE = False
try:
    patch_pillow_compatibility()  # 先修补兼容性
    import ddddocr
    DDDDOCR_AVAILABLE = True
    print("[+] ddddocr库已加载，将使用ddddocr识别验证码")
except ImportError as e:
    print(f"[-] ddddocr库加载失败: {e}")
    print("[-] 请运行: pip install ddddocr")
except Exception as e:
    print(f"[-] ddddocr初始化失败: {e}")

if not DDDDOCR_AVAILABLE:
    print("[-] ddddocr不可用，将回退到手动输入验证码模式")

def clean_ocr_result(text):
    """
    清理OCR识别结果
    Args:
        text: 原始OCR识别文本
    Returns:
        str: 清理后的文本
    """
    if not text:
        return ""

    # 移除空格和换行符
    text = text.strip().replace('\n', '').replace('\r', '').replace(' ', '')

    # 只保留字母数字
    cleaned = ''.join(char for char in text if char.isalnum())

    # 常见字符混淆修正
    replacements = {
        'O': '0',  # 字母O替换为数字0
        'o': '0',  # 小写o替换为数字0
        'I': '1',  # 字母I替换为数字1
        'l': '1',  # 小写l替换为数字1
        'S': '5',  # 字母S有时会被误识别
        'G': '6',  # 字母G有时会被误识别
        'B': '8',  # 字母B有时会被误识别
        'Z': '2',  # 字母Z有时会被误识别为2
    }

    # 应用替换规则（仅当结果看起来像验证码时）
    if len(cleaned) >= 3 and cleaned.isalnum():
        for old, new in replacements.items():
            cleaned = cleaned.replace(old, new)

    return cleaned.upper()  # 转换为大写保持一致性


def get_access_code(session, main_system_url="https://apollo.siyscrm.com"):
    """
    调用GetAdmpCode API获取最新的access_code
    注意：这个API在主系统域名下，不是登录域名下
    """
    api_url = f"{main_system_url}/Account/UserInfo/GetAdmpCode"

    headers = {
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Origin': main_system_url,
        'Referer': f"{main_system_url}/",
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest',
        'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
    }

    try:
        print(f"[+] 调用GetAdmpCode API: {api_url}")
        response = session.post(api_url, headers=headers, data={})
        response.raise_for_status()

        print(f"[+] API响应状态码: {response.status_code}")

        # 尝试解析JSON响应
        result = response.json()
        print(f"[+] API响应内容: {result}")

        if result.get("Status") == 1 and result.get("Result"):
            access_code = result["Result"].get("accessCode")
            platform_data = result["Result"].get("platformData")

            if access_code:
                print(f"[+] 获取到accessCode: {access_code}")
                if platform_data:
                    print(f"[+] 获取到platformData: {platform_data}")
                return access_code
            else:
                print("[-] API响应中没有accessCode字段")
                return None
        else:
            print(f"[-] API调用失败: {result.get('Message', '未知错误')}")
            return None

    except Exception as e:
        print(f"[-] 调用GetAdmpCode API异常: {e}")
        return None


def build_token_exact(username, password, client_id, redirect_uri, access_code, timestamp):
    """基于真实浏览器请求的精确token构造"""
    # 注意：真实浏览器中使用的是冒号分隔，不是等号
    token_data = {
        "name": username,
        "pwd": password,
        "redirect_uri": redirect_uri,
        "client_id": client_id,
        "access_code": access_code,
        "timestamp": timestamp
    }

    # 转换为JSON字符串（无空格）
    json_str = json.dumps(token_data, separators=(',', ':'))

    # 第一步：URL编码
    url_encoded = quote(json_str)

    # 第二步：Base64编码
    base64_encoded = base64.b64encode(url_encoded.encode('utf-8')).decode('utf-8')

    # 第三步：再次URL编码
    final_token = quote(base64_encoded)

    return final_token

def decode_token_for_debug(token):
    """解码token用于调试"""
    try:
        # 第一步：URL解码
        url_decoded = quote(token, safe='')
        # 第二步：Base64解码
        base64_decoded = base64.b64decode(url_decoded).decode('utf-8')
        # 第三步：再次URL解码
        final_decoded = quote(base64_decoded, safe='')
        print(f"[DEBUG] Token解码结果: {final_decoded}")
        return final_decoded
    except Exception as e:
        print(f"[DEBUG] Token解码失败: {e}")
        return None

def try_login_with_known_good_values(session, base_url, login_page_url, img_code, img_code_key):
    """使用从浏览器捕获的已知正确值进行登录尝试"""
    print("[+] 尝试使用浏览器捕获的已知正确值...")

    # 从浏览器网络捕获中获得的已知正确值
    known_good_token = "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

    # 但是我们需要更新时间戳和验证码
    # 解码已知token以获取结构
    try:
        decoded_base64 = base64.b64decode(known_good_token).decode('utf-8')
        decoded_url = urllib.parse.unquote(decoded_base64)
        token_data = json.loads(decoded_url)

        # 更新时间戳和验证码相关信息
        token_data["timestamp"] = int(time.time() * 1000)

        # 重新构建token（使用简单的方法，不是三重编码）
        json_str = json.dumps(token_data, separators=(',', ':'))
        url_encoded = urllib.parse.quote(json_str)
        updated_token = base64.b64encode(url_encoded.encode('utf-8')).decode('utf-8')

        print(f"[+] 使用已知access_code: {token_data['access_code']}")
        print(f"[+] 更新后的timestamp: {token_data['timestamp']}")

        return updated_token, token_data['access_code']

    except Exception as e:
        print(f"[-] 处理已知token失败: {e}")
        return None, None

def working_login_solution():
    """基于真实成功请求的工作解决方案"""
    session = requests.Session()
    base_url = "https://login-sso.siyscrm.com"
    
    # 用户凭据
    username = "***********"
    password = "aabb6688"
    client_id = "81c4e66b04066aa8da079f3e5d2bf1f3"
    redirect_uri = "https://apollo.siyscrm.com/account/login/loginsuccess"
    
    print("私域网站爬虫 - 最终工作解决方案")
    print("=" * 60)
    
    # 1. 访问登录页面建立会话
    login_page_url = f"{base_url}/html/apollo_v4/index.html?redirect_uri=https%3a%2f%2fapollo.siyscrm.com%2faccount%2flogin%2floginsuccess&client_id=81c4e66b04066aa8da079f3e5d2bf1f3"
    
    page_headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Host': 'login-sso.siyscrm.com',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
    }
    
    try:
        page_response = session.get(login_page_url, headers=page_headers)
        page_response.raise_for_status()
        print(f"[+] 登录页面访问成功: {page_response.status_code}")
    except Exception as e:
        print(f"[-] 访问登录页面失败: {e}")
        return False
    
    # 2. 获取验证码key
    print("[+] 获取验证码key...")
    key_url = f"{base_url}/Authorize/GetImgVerifyCodeKey"
    xhr_headers = {
        'Accept': '*/*',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
        'Host': 'login-sso.siyscrm.com',
        'Referer': login_page_url,
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest',
        'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
    }
    
    try:
        key_response = session.get(key_url, headers=xhr_headers)
        key_response.raise_for_status()
        img_code_key = key_response.json()
        print(f"[+] 验证码key: {img_code_key}")
    except Exception as e:
        print(f"[-] 获取验证码key失败: {e}")
        return False
    
    # 3. 下载验证码图片并自动识别
    print("[+] 下载验证码图片...")
    img_url = f"{base_url}/Authorize/GetImgVerifyCode?key={img_code_key}"
    try:
        img_response = session.get(img_url, headers=xhr_headers)
        img_response.raise_for_status()

        captcha_filename = f'captcha_{int(time.time())}.jpg'
        with open(captcha_filename, 'wb') as f:
            f.write(img_response.content)
        print(f"[+] 验证码已保存: {captcha_filename}")
    except Exception as e:
        print(f"[-] 下载验证码失败: {e}")
        return False

    # 4. 使用ddddocr自动识别验证码
    img_code = None
    if DDDDOCR_AVAILABLE:
        print("[+] 使用ddddocr自动识别验证码...")
        try:
            # 初始化ddddocr（兼容不同版本）
            try:
                # 尝试新版本的初始化方式
                ocr = ddddocr.DdddOcr(show_ad=False)
            except TypeError:
                # 如果不支持show_ad参数，使用默认初始化
                ocr = ddddocr.DdddOcr()

            print("[+] ddddocr初始化成功")

            # 给验证码图片一些时间完全写入磁盘
            time.sleep(1)

            # 读取验证码图片并识别
            with open(captcha_filename, 'rb') as f:
                img_bytes = f.read()

            print(f"[+] 验证码图片大小: {len(img_bytes)} bytes")

            raw_result = ocr.classification(img_bytes)
            print(f"[+] ddddocr原始识别结果: '{raw_result}'")

            # 清理识别结果
            img_code = clean_ocr_result(raw_result)
            print(f"[+] 清理后的验证码: '{img_code}'")

            # 验证结果是否合理
            if img_code and 3 <= len(img_code) <= 8:
                print(f"[+] ddddocr识别成功: {img_code}")
            else:
                print(f"[-] ddddocr识别结果不合理，长度: {len(img_code) if img_code else 0}")
                img_code = None

        except Exception as e:
            print(f"[-] ddddocr识别失败: {e}")
            img_code = None

    # 如果ddddocr识别失败或不可用，回退到手动输入
    if not img_code:
        print("[!] 自动识别失败，回退到手动输入模式")
        print(f"[!] 验证码图片已保存: {captcha_filename}")
        img_code = input("请输入验证码: ").strip()

        if not img_code:
            print("[-] 验证码不能为空")
            return False
    
    # 5. 执行预检查（完全按照浏览器顺序）
    print("[+] 执行预检查...")
    try:
        # 检查登录验证要求
        verify_url = f"{base_url}/Authorize/GetLoginVerifCode?userAccount={username}&clientId={client_id}"
        verify_response = session.get(verify_url, headers=xhr_headers)
        print(f"[+] 登录验证检查: {verify_response.status_code}")
        print(f"[DEBUG] 登录验证检查响应: {verify_response.text}")
        
        # 获取用户信息
        mobile_url = f"{base_url}/Authorize/GetUserMobile?account={username}"
        mobile_response = session.get(mobile_url, headers=xhr_headers)
        print(f"[+] 用户信息检查: {mobile_response.status_code}")
        print(f"[DEBUG] 用户信息检查响应: {mobile_response.text}")
    except Exception as e:
        print(f"[-] 预检查失败: {e}")
    
    # 6. 尝试多个已知的access_code进行登录
    print("[+] 尝试使用已知access_code进行登录...")
    known_access_codes = [
        "D358CD4F780378EE63257EBB591B6129",  # 最新的
        "4ED6EF22C6B165704CF33415CBBB2D9",
        "6D34DFCDAC40CAD1033CB633BFE78876",
        "0ED644807DC4876D6771F992CC08FF97"
    ]

    # 尝试每个access_code，直到找到一个有效的
    login_success = False
    for i, access_code in enumerate(known_access_codes):
        print(f"[+] 尝试access_code {i+1}/{len(known_access_codes)}: {access_code}")

        # 构造登录参数
        timestamp = int(time.time() * 1000)
        token = build_token_exact(username, password, client_id, redirect_uri, access_code, timestamp)

        # 尝试登录
        if try_login_with_token(session, base_url, token, img_code, img_code_key):
            print(f"[+] 登录成功！使用的access_code: {access_code}")
            login_success = True
            break
        else:
            print(f"[-] access_code {access_code} 无效，尝试下一个...")

    if not login_success:
        print("[-] 所有已知access_code都无效，登录失败")
        return False

    return True
        
    # 7. 构造登录参数（使用获取到的Access Code）
    print("[+] 构造登录参数...")
    timestamp = int(time.time() * 1000)
    token = build_token_exact(username, password, client_id, redirect_uri, access_code, timestamp)

    print(f"[+] Access Code: {access_code}")
    print(f"[+] Timestamp: {timestamp}")
    print(f"[+] Token: {token[:50]}...")

    # 调试：解码token查看内容
    decode_token_for_debug(token)
    
    # 7. 发送登录请求（完全按照真实浏览器请求）
    login_url = f"{base_url}/Authorize/postV2"
    login_headers = {
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json',
        'Host': 'login-sso.siyscrm.com',
        'Origin': 'https://login-sso.siyscrm.com',
        'Referer': login_page_url,
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest',
        'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'token': token  # 关键：token在请求头中
    }
    
    # 构造请求体（完全按照真实请求）
    # 注意：根据浏览器网络捕获，这些字段在请求体中确实是空的，
    # 真实的用户信息在token中编码传递
    login_payload = {
        'name': '',
        'pwd': '',
        'redirect_uri': '',
        'client_id': '',
        'img_verify_code': img_code,
        'img_verify_code_key': img_code_key,
        'is_mobile_verify': False
    }
    
    try:
        print("[+] 发送登录请求...")
        
        # 设置不跟随重定向，这样我们可以捕获到登录成功的响应
        login_response = session.post(login_url, headers=login_headers, json=login_payload, allow_redirects=False)
        
        print(f"[+] 响应状态码: {login_response.status_code}")
        print(f"[+] 响应头: {dict(login_response.headers)}")
        
        # 检查响应
        if login_response.status_code == 200:
            try:
                # 尝试解析JSON响应
                result = login_response.json()
                print(f"[+] 登录响应: {result}")

                if result.get("Status") == 0:
                    print("[+] 登录成功!")
                    if result.get("Result"):
                        print(f"[+] 重定向URL: {result['Result']}")

                        # 可选：访问重定向URL验证登录状态
                        try:
                            redirect_response = session.get(result['Result'])
                            if redirect_response.status_code == 200:
                                print("[+] 成功访问目标系统!")
                                print("[+] 登录成功后的页面内容:")
                                print("=" * 80)
                                print(redirect_response.text[:2000])  # 打印前2000个字符
                                print("=" * 80)

                                # 登录成功后，调用GetAdmpCode API获取最新的access_code
                                print("[+] 获取最新的access_code...")
                                access_code = get_access_code(session)
                                if access_code:
                                    print(f"[+] 成功获取access_code: {access_code}")
                                else:
                                    print("[-] 获取access_code失败，但登录成功")

                                return True
                        except Exception as e:
                            print(f"访问重定向URL时出错: {e}")
                            pass

                    # 即使没有重定向URL，也尝试获取access_code
                    print("[+] 获取最新的access_code...")
                    access_code = get_access_code(session)
                    if access_code:
                        print(f"[+] 成功获取access_code: {access_code}")
                    else:
                        print("[-] 获取access_code失败，但登录成功")

                    return True
                elif result.get("Status") == 2 and "验证码" in result.get("Message", ""):
                    print(f"[!] 验证码错误: {result.get('Message')}")
                    print("[!] HTTP请求方法是正确的！只是验证码识别有问题")
                    print("[!] 建议：集成OCR库自动识别验证码，或使用Selenium方案")
                    return False
                else:
                    print(f"[-] 登录失败: {result.get('Message', '未知错误')}")
                    return False
                    
            except json.JSONDecodeError:
                # 如果不是JSON响应，检查是否是重定向成功
                response_text = login_response.text
                print(f"[+] 非JSON响应: {response_text[:200]}")
                
                if "apollo.siyscrm.com" in response_text or login_response.status_code == 200:
                    print("[+] 登录成功 (非JSON响应)!")
                    return True
                return False

        elif login_response.status_code in [301, 302, 303, 307, 308]:
            # 重定向响应，通常表示登录成功
            location = login_response.headers.get('Location', '')
            print(f"[+] 登录成功! 重定向到: {location}")

            if location:
                try:
                    redirect_response = session.get(location)
                    if redirect_response.status_code == 200:
                        print("[+] 成功访问目标系统!")
                        print("[+] 登录成功后的页面内容:")
                        print("=" * 80)
                        print(redirect_response.text[:2000])  # 打印前2000个字符
                        print("=" * 80)
                except Exception as e:
                    print(f"访问重定向URL时出错: {e}")
                    pass
            return True
            
        else:
            print(f"[-] 登录请求失败: {login_response.status_code}")
            print(f"[-] 响应内容: {login_response.text}")
            return False
            
    except Exception as e:
        print(f"[-] 登录请求异常: {e}")
        return False
    
    finally:
        # 清理验证码文件
        try:
            if os.path.exists(captcha_filename):
                os.remove(captcha_filename)
                print(f"[+] 已清理验证码文件")
        except:
            pass

def main():
    """主函数"""
    success = working_login_solution()
    
    print("=" * 60)
    if success:
        print("[+] 登录流程成功完成!")
        print("[+] 验证码自动识别功能已集成")
        print("[+] 使用ddddocr实现验证码自动识别")
        print("[+] 完全自动化登录已实现")
    else:
        print("[-] 登录流程失败")
        print("[-] 请检查验证码识别是否正确或网络连接")
        print("[-] 如果ddddocr识别率低，可考虑使用其他OCR方案")
    print("=" * 60)

if __name__ == "__main__":
    main()
